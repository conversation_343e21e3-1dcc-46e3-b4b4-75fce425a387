{"name": "@vuetify/api-generator", "type": "module", "version": "3.8.11", "private": true, "description": "", "scripts": {"build": "node --import tsx src/index.ts", "lint": "eslint --ext .ts,.json src -f codeframe --max-warnings 0", "lint:fix": "pnpm lint --fix"}, "author": "", "license": "ISC", "dependencies": {"deepmerge": "^4.3.1", "piscina": "^4.9.2", "prettier": "^3.5.3", "ts-morph": "^25.0.1", "tsx": "^4.19.3", "vue": "^3.5.13", "vuetify": "workspace:*"}, "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/stringify-object": "^4.0.5", "eslint": "^8.57.0", "lodash-es": "^4.17.21", "stringify-object": "^5.0.0"}}