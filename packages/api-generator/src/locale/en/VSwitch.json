{"props": {"flat": "Display component without elevation. Default elevation for thumb is 4dp, `flat` resets it.", "inputValue": "The **v-model** bound value.", "indeterminate": "Sets an indeterminate state for the switch.", "inset": "Enlarge the `v-switch` track to encompass the thumb.", "loading": "Displays circular progress bar. Can either be a String which specifies which color is applied to the progress bar (any material color or theme color - primary, secondary, success, info, warning, error) or a Boolean which uses the component color (set by color prop - if it's supported by the component) or the primary color.", "multiple": "Changes expected model to an array."}, "events": {"change": "Emitted when the input is changed by user interaction.", "update:indeterminate": "Event that is emitted when the component's indeterminate state changes.", "click": "Emitted when input is clicked. **Note:** the **change** event should be used instead of **click** when monitoring state change."}, "slots": {"thumb": "Slot for custom thumb content.", "track-true": "Slot for custom track content when value is true.", "track-false": "Slot for custom track content when value is false."}}