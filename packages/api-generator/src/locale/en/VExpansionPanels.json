{"props": {"accordion": "Removes the margin around open panels.", "expand": "Leaves the expansion-panel open while selecting another.", "focusable": "Makes the expansion-panel headers focusable.", "flat": "Removes the expansion-panel's elevation and borders.", "hover": "Applies a background-color shift on hover to expansion panel headers.", "inset": "Makes the expansion panel open with a inset style.", "popout": "Makes the expansion-panel open with an popout style.", "readonly": "Makes the entire expansion panel read only.", "static": "Remove title size expansion when selected.", "tile": "Removes the border-radius.", "value": "Controls the opened/closed state of content in the expansion-panel. Corresponds to a zero-based index of the currently opened content. If the `multiple` prop (previously `expand` in 1.5.x) is used then it is an array of numbers where each entry corresponds to the index of the opened content.  The index order is not relevant."}, "exposed": {"prev": "Open the previous panel from the currently selected panel.", "next": "Open the next panel from the currently selected panel."}}