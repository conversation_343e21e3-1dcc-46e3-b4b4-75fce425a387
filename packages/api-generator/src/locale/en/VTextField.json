{"props": {"autofocus": "Enables autofocus.", "clearIcon": "Applied when using **clearable** and the input is dirty.", "counterValue": "Function returns the counter display text.", "flat": "Removes elevation (shadow) added to element when using the **solo** or **solo-inverted** props.", "persistentPlaceholder": "Forces placeholder to always be visible.", "placeholder": "Sets the input’s placeholder text.", "prefix": "Displays prefix text.", "prependIcon": "Prepends an icon to the outside the component's input, uses the same syntax as `v-icon`.", "prependInnerIcon": "Prepends an icon inside the component's input, uses the same syntax as `v-icon`.", "reverse": "Reverses the input orientation.", "role": "The role attribute applied to the input.", "rounded": "Adds a border radius to the input.", "suffix": "Displays suffix text.", "toggleKeys": "Array of key codes that will toggle the input (if it supports toggling).", "type": "Sets input type."}, "events": {"blur": "Emitted when the input is blurred.", "change": "Emitted when the input is changed by user interaction.", "click:append": "Emitted when append icon is clicked.", "click:append-outer": "Emitted when appended outer icon is clicked.", "click:appendInner": "Emitted when appended inner icon is clicked.", "click:clear": "Emitted when clearable icon clicked.", "click:prepend": "Emitted when prepended icon is clicked.", "click:prepend-inner": "Emitted when prepended inner icon is clicked.", "click:prependInner": "Emitted when prepended inner icon is clicked.", "focus": "Emitted when component is focused.", "keydown": "Emitted when **any** key is pressed.", "mousedown:control": "Event that is emitted when using mousedown on the main control area."}, "slots": {"counter": "Slot for the input’s counter text."}}