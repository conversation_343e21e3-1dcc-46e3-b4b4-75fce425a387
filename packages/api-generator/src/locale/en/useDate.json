{"exposed": {"addWeeks": "Adds the specified number of weeks to the date.", "addDays": "Adds the specified number of days to the date.", "addMonths": "Adds the specified number of months to the date.", "addHours": "Adds the specified number of hours to the date.", "addMinutes": "Adds the specified number of minutes to the date.", "date": "Takes any value and returns a date object.", "endOfDay": "Returns the last second of the day.", "endOfMonth": "Returns the last day of the month.", "endOfWeek": "Returns the last day of the week.", "endOfYear": "Returns the last day of the year.", "format": "Takes a date object and returns it in a specified format.", "getDiff": "Returns the difference between two dates in the specified unit.", "getMonth": "Returns the month of the date.", "getWeek": "Returns the week of the year of the date.", "getWeekArray": "Returns an array of the days of the week of the date.", "getWeekdays": "Returns an array of the names of the days of the week.", "getDate": "Returns the day of the month of the date.", "getHours": "Returns the hours of the day of the date.", "getMinutes": "Returns the minutes of the hour of the date.", "getNextMonth": "Returns the next month of the date.", "getPreviousMonth": "Returns the previous month of the date.", "parseISO": "Parses a date string in ISO format.", "isAfter": "Returns true if the first date is after the second date.", "isAfterDay": "Returns true if the first date is after the second date.", "isBefore": "Returns true if the first date is before the second date.", "isEqual": "Returns true if the two dates are equal.", "isSameDay": "Returns true if the two dates are the same day.", "isSameMonth": "Returns true if the two dates are the same month.", "isSameYear": "Returns true if the two dates are the same year.", "isValid": "Returns true if the date is valid.", "isWithinRange": "Returns true if the first date is within the range of the second and third dates.", "locale": "Returns the current locale being used.", "getYear": "Returns the year of the date.", "setYear": "Sets the year of the date.", "setMonth": "Sets the month of the date.", "setDate": "Sets the day of the date.", "setHours": "Sets the hours of the date.", "setMinutes": "Sets the minutes of the date.", "startOfDay": "Returns the first second of the day.", "startOfMonth": "Returns first day of the month.", "startOfWeek": "Returns the first day of the week.", "startOfYear": "Returns the first day of the year.", "toJsDate": "Converts date value to a JS Date Object.", "toISO": "Converts date value to a ISO Date Object."}}