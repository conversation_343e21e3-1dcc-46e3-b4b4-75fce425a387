{"props": {"dayFormat": "Formats day of the month string that appears in a day to a specified locale.", "end": "The ending date on the calendar (inclusive) in the format of `YYYY-MM-DD`. This may be ignored depending on the `type` of the calendar.", "hideHeader": "If the header at the top of the `day` view should be visible.", "locale": "The locale of the calendar.", "localeFirstDayOfYear": "Sets the day that determines the first week of the year, starting with 0 for **Sunday**. For ISO 8601 this should be 4.", "minWeeks": "The minimum number of weeks to display in the `month` or `week` view.", "monthFormat": "Formats month string that appears in a day to specified locale.", "now": "Override the day & time which is considered now. This is in the format of `YYYY-MM-DD hh:mm:ss`. The calendar is styled according to now.", "shortMonths": "Whether the short versions of a month should be used (Jan vs January).", "shortWeekdays": "Whether the short versions of a weekday should be used (Mon vs Monday).", "showMonthOnFirst": "Whether the name of the month should be displayed on the first day of the month.", "showWeek": "Whether week numbers should be displayed when using the `month` view.", "start": "The starting date on the calendar (inclusive) in the format of `YYYY-MM-DD`. This may be ignored depending on the `type` of the calendar.", "weekdayFormat": "Formats day of the week string that appears in the header to specified locale.", "weekdays": "Specifies which days of the week to display. To display Monday through Friday only, a value of `[1, 2, 3, 4, 5]` can be used. To display a week starting on Monday a value of `[1, 2, 3, 4, 5, 6, 0]` can be used."}}