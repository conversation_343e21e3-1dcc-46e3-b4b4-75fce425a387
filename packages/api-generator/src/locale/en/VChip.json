{"props": {"active": "Determines whether the chip is visible or not.", "closable": "Adds remove button and then a chip can be closed.", "close": "Adds remove button.", "closeIcon": "Change the default icon used for **close** chips.", "closeLabel": "Text used for *aria-label* on the close button in **close** chips. Can also be customized globally in [Internationalization](/customization/internationalization).", "draggable": "Makes the chip draggable.", "filter": "Displays a selection icon when selected.", "filterIcon": "Change the default icon used for **filter** chips.", "inputValue": "Controls the **active** state of the item. This is typically used to highlight the component.", "label": "Applies a medium size border radius.", "outlined": "Removes background and applies border and text color.", "pill": "Remove `v-avatar` padding.", "size": "Sets the height, padding and the font size of the component. Accepts only predefined options: **x-small**, **small**, **default**, **large**, and **x-large**.", "value": "The value used when a child of a [v-chip-group](/components/chip-groups)."}, "events": {"click": "Emitted when component is clicked, toggles chip if contained in a chip group - Will trigger component to ripple when clicked unless the `.native` modifier is used."}, "slots": {"close": "Slot for icon used in **close** prop.", "filter": "Slot for icon used in **filter** prop."}}