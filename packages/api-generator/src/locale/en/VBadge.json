{"props": {"bordered": "Applies a **2px** by default and **1.5px** border around the badge when using the **dot** property.", "content": "Text content to show in the badge.", "dot": "Reduce the size of the badge and hide its contents.", "floating": "Move the badge further away from the slotted content. Equivalent to an 8px offset.", "inline": "Display as an inline block instead of absolute position. **location**, **floating**, and **offset** will have no effect.", "label": "The **aria-label** used for the badge.", "max": "Sets the maximum number allowed when using the **content** prop with a `number` like value. If the content number exceeds the maximum value, a `+` suffix is added.", "offsetX": "Offset the badge on the x-axis.", "offsetY": "Offset the badge on the y-axis.", "modelValue": "Controls whether the component is visible or hidden."}, "slots": {"badge": "The slot used for the badge's content."}}