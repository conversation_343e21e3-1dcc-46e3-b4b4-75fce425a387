{"props": {"direction": "Specifies if scroller is **vertical** or **horizontal**.", "emptyText": "Text shown when there is no more content to load.", "loadMoreText": "Text shown in default load more button, when in manual mode.", "margin": "Value sent to the intersection observer. Will make the observer trigger earlier, by the margin (px) value supplied.", "mode": "Specifies if content should load automatically when scrolling (**intersect**) or manually (**manual**).", "side": "Specifies the side where new content should appear. Either the **start**, **end**, or **both** sides."}, "slots": {"empty": "Shown when load returned status 'empty'.", "error": "Shown when load returned status 'error'.", "load-more": "Shown when scrolled to either side of the content, in manual mode.", "loading": "Shown when content is loading."}, "events": {"load": "Emitted when reaching the start / end threshold, or if triggered when using manual mode."}}