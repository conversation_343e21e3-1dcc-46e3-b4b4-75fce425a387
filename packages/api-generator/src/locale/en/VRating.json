{"props": {"ariaLabel": "The **aria-label** used for each item.", "clearable": "Allows for the component to be cleared by clicking on the current value.", "emptyIcon": "The icon displayed when empty.", "fullIcon": "The icon displayed when full.", "itemLabels": "Array of labels to display next to each item..", "itemLabelPosition": "Position of item labels. Accepts 'top' and 'bottom'.", "halfIcon": "The icon displayed when half (requires **half-increments** prop).", "halfIncrements": "Allows the selection of half increments.", "hover": "Provides visual feedback when hovering over icons.", "length": "The amount of items to show.", "readonly": "Removes all hover effects and pointer events.", "itemAriaLabel": "The **aria-label** used for each item."}, "slots": {"item": "The slot for each item.", "item-label": "The slot for each item label."}}