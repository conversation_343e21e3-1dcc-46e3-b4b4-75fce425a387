{"props": {"autoGrow": "Automatically grow the textarea depending on amount of text.", "counterValue": "Display the input length but do not provide any validation.", "noResize": "Remove resize handle.", "persistentPlaceholder": "Forces placeholder to always be visible.", "prefix": "Displays prefix text.", "rows": "Default row count.", "suffix": "Displays suffix text.", "maxRows": "Specifies the maximum number of rows for **auto-grow**.", "autofocus": "The element should be focused as soon as the page loads."}, "events": {"keydown": "Emitted when **any** key is pressed, textarea must be focused.", "mousedown:control": "Event that is emitted when using mousedown on the main control area."}, "slots": {"counter": "Slot for the input’s counter text."}}