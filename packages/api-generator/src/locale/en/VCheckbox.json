{"props": {"column": "Displays radio buttons in column.", "dark": "Applies the dark theme variant to the component. This will default the components color to _white_ unless you've configured your [application theme](/customization/theme) to **dark** or if you are using the **color** prop on the component. You can find more information on the Material Design documentation for [dark themes](https://material.io/design/color/dark-theme.html).", "falseIcon": "The icon used when inactive.", "flat": "Display component without elevation. Default elevation for thumb is 4dp, `flat` resets it.", "indeterminate": "Sets an indeterminate state for the checkbox.", "indeterminateIcon": "The icon used when in an indeterminate state.", "inputValue": "The **v-model** bound value.", "inset": "Enlarge the `v-switch` track to encompass the thumb.", "multiple": "Changes expected model to an array.", "row": "Displays radio buttons in row.", "trueIcon": "The icon used when active."}, "events": {"change": "Emitted when the input is changed by user interaction."}}