{"props": {"absolute": "Applies position: absolute to the component.", "alt": "Alternate text for screen readers. Leave empty for decorative images.", "aspectRatio": "Calculated as `width/height`, so for a 1920x1080px image this will be `1.7778`. Will be calculated automatically if omitted.", "cover": "Resizes the background image to cover the entire container.", "draggable": "Controls the `draggable` behavior of the image. See [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/draggable).", "lazySrc": "Something to show while waiting for the main image to load, typically a small base64-encoded thumbnail. Has a slight blur filter applied.  \nNOTE: This prop has no effect unless either `height` or `aspect-ratio` are provided.", "crossorigin": "Specify that images should be fetched with CORS enabled [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#crossorigin)", "position": "Applies [object-position](https://developer.mozilla.org/en-US/docs/Web/CSS/object-position) styles to the image and placeholder elements.", "referrerpolicy": "Define which referrer is sent when fetching the resource [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#referrerpolicy)", "options": "Options that are passed to the [Intersection observer](https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API) constructor.", "sizes": "For use with `srcset`, see [MDN](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#attr-sizes).", "src": "The image URL. This prop is mandatory.", "srcset": "A set of alternate images to use based on device size. [Read more...](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/img#attr-srcset).", "transition": "The transition to use when switching from `lazy-src` to `src`. Can be one of the [built in](/styles/transitions/) or custom transition.", "gradient": "The gradient to apply to the image. Only supports [linear-gradient](https://developer.mozilla.org/en-US/docs/Web/CSS/gradient/linear-gradient) syntax, anything else should be done with classes."}, "slots": {"placeholder": "Display an overlay while the image is loading.", "error": "Will be shown if the image fails to load, replacing the placeholder slot.", "sources": "A list of `<source>` elements. If this slot is used v-img will render a `<picture>` instead of `<img>`."}, "events": {"error": "Emitted if the image fails to load.", "load": "Emitted when the image is loaded.", "loadstart": "Emitted when the image starts to load."}, "exposed": {"currentSrc": "The current source of the image. This is the image that is currently being displayed. This is useful for determining if the image is loading or not.", "image": "The image element.", "naturalHeight": "The natural height of the image.", "naturalWidth": "The natural width of the image.", "state": "The current state of the image. This is useful for determining if the image is loading or not."}}