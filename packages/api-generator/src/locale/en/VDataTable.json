{"props": {"calculateWidths": "Enables calculation of column widths. `widths` property will be available in select scoped slots.", "checkboxColor": "Set the color of the checkboxes (showSelect must be used).", "customFilter": "Function to filter items.", "customGroup": "Function used to group items.", "customSort": "Function used to sort items.", "caption": "Set the caption (using `<caption>`).", "density": "Adjusts the vertical height of the table rows.", "disableFiltering": "Disables filtering completely.", "disablePagination": "Disables pagination completely.", "disableSort": "Disables sorting completely.", "expandIcon": "Icon used for expand toggle button.", "fixedHeader": "Fixed header to top of table.", "groupBy": "Changes which item property should be used for grouping items. Currently only supports a single grouping in the format: `group` or `['group']`. When using an array, only the first element is considered. Can be used with `.sync` modifier.", "groupDesc": "Changes which direction grouping is done. Can be used with `.sync` modifier.", "headerProps": "Pass props to the default header. See [`v-data-table-headers` API](/api/v-data-table-headers) for more information.", "headers": "An array of objects that each describe a header column.", "headersLength": "Can be used in combination with `hide-default-header` to specify the number of columns in the table to allow expansion rows and loading bar to function properly.", "height": "Set an explicit height of table.", "hover": "Adds a hover effects to a table rows.", "itemClass": "Property on supplied `items` that contains item's row class or function that takes an item as an argument and returns the class of corresponding row.", "itemsPerPage": "Changes how many items per page should be visible. Can be used with `.sync` modifier. Setting this prop to `-1` will display all items on the page.", "locale": "Sets the locale used for sorting. This is passed into [`Intl.Collator()`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Collator/Collator) in the default `customSort` function.", "multiSort": "If `true` then one can sort on multiple properties.", "mustSort": "If `true` then one can not disable sorting, it will always switch between ascending and descending.", "page": "The current displayed page number (1-indexed).", "serverItemsLength": "Used only when data is provided by a server. Should be set to the total amount of items available on server so that pagination works correctly.", "showSelect": "Shows the select checkboxes in both the header and rows (if using default rows).", "showExpand": "Shows the expand toggle in default rows.", "showGroupBy": "Shows the group by toggle in the header and enables grouped rows.", "sortBy": "Changes which item property (or properties) should be used for sort order. Can be used with `.sync` modifier.", "sortDesc": "Changes which direction sorting is done. Can be used with `.sync` modifier.", "virtualRows": "Virtualizes the rendering of rows. Be aware that you can not use the `body`, `body.prepend` or `body.append` slots with this prop."}, "slots": {"[`header.${string}`]": "Slot for custom rendering of a header cell.", "[`item.${string}`]": "Slot for custom rendering of a row cell.", "header.data-table-expand": "Slot to replace the default `v-icon` used when expanding header.", "header": "Slot to replace the default table `<thead>`.", "body": "Slot to replace the default table `<tbody>`.", "body.append": "Appends elements to the end of the default table `<tbody>`.", "body.prepend": "Prepends elements to the start of the default table `<tbody>`.", "bottom": "Slot for custom rendering of a data table footer.", "colgroup": "Slot to replace the default rendering of the `<colgroup>` element.", "foot": "Slot to add a `<tfoot>` element after the `<tbody>`. Not to be confused with the `footer` slot.", "expanded-item": "Slot to customize expanded rows.", "footer": "Slot to add a custom footer.", "footer.prepend": "Adds content to the empty space in the footer.", "footer.page-text": "Slot to customize footer page text.", "group": "Slot to replace the default rendering of grouped rows.", "group.header": "Slot to customize the default rendering of group headers.", "group.summary": "Slot to customize the default rendering of group summaries.", "heading": "Slot to add a custom header.", "header.<name>": "Slot to customize a specific header column.", "header.data-table-select": "Slot to replace the default `v-checkbox-btn` in header.", "headers": "An array of objects that each describe a header column.", "item": "Slot to replace the default rendering of a row.", "item.data-table-select": "Slot to replace the default `v-checkbox-btn` used when selecting rows.", "item.data-table-expand": "Slot to replace the default `v-icon` used when expanding rows.", "item.<name>": "Slot to customize a specific column.", "loading": "Defines content for when `loading` is true and no items are provided.", "tbody": "Slot to replace the default table `<tbody>`.", "thead": "Slot to replace the default table `<thead>`.", "tfoot": "Slot to replace the default table `<tfoot>`.", "no-data": "Defines content for when no items are provided.", "no-results": "Defines content for when `search` is provided but no results are found.", "progress": "Slot to replace the default `<v-progress-linear>` component.", "top": "Slot to add content above the table."}, "events": {"click:row": "Emits when a table row is clicked. This event provides 2 arguments: the first is the native click event, and the second is an object containing the corresponding item for that row. **NOTE:** will not emit when table rows are defined through a slot such as `item` or `body`.", "contextmenu:row": "Emits when a table row is right-clicked. The item for the row is included. **NOTE:** will not emit when table rows are defined through a slot such as `item` or `body`.", "dblclick:row": "Emits when a table row is double-clicked. The item for the row is included. **NOTE:** will not emit when table rows are defined through a slot such as `item` or `body`.", "currentItems": "Emits the items provided via the **items** prop, every time the internal **computedItems** is changed.", "pageCount": "Emits when the **pageCount** property of the **pagination** prop is updated.", "pagination": "Emits when something changed to the `pagination` which can be provided via the `pagination` prop.", "toggleSelectAll": "Emits when the `select-all` checkbox in table header is clicked. This checkbox is enabled by the **show-select** prop.", "update:currentItems": "Emits with the items currently being displayed.", "update:expanded": "Emits when the **expanded** prop is updated.", "update:groupBy": "Emits when the **group-by** prop is updated.", "update:groupDesc": "Emits when the **group-desc** prop is updated.", "update:itemsPerPage": "Emits when the **items-per-page** prop is updated.", "update:modelValue": "Emits when the component's model changes.", "update:multiSort": "Emits when the **multi-sort** prop is updated.", "update:mustSort": "Emits when the **must-sort** prop is updated.", "update:options": "Emits when pagination related properties (page, itemsPerPage, sortBy, groupBy, search) is updated.", "update:page": "Emits when the **page** prop is updated.", "update:sortBy": "Emits when the **sortBy** prop is updated.", "update:sortDesc": "Emits when the **sort-desc** prop is updated."}}