{"props": {"backgroundColor": "Changes the background-color of the input.", "baseColor": "Sets the color of the input when it is not focused.", "centerAffix": "Vertically align **appendInner**, **prependInner**, **clearIcon** and **label** in the center.", "direction": "Changes the direction of the input.", "hideDetails": "Hides hint and validation errors. When set to `auto` messages will be rendered only if there's a message (hint, error message, counter value etc) to display.", "hideSpinButtons": "Hides spin buttons on the input when type is set to `number`.", "dense": "Reduces the input height.", "glow": "Makes prepend/append icons full opacity when the input is focused and apply color.", "height": "Sets the height of the input.", "hint": "Displays hint text below the input when focused. Force this always open with the [persistent-hint](#props-persistent-hint) property.", "iconColor": "Sets the color of the prepend/append icons.", "id": "Sets the DOM id on the component.", "loading": "Displays linear progress bar. Can either be a String which specifies which color is applied to the progress bar (any material color or theme color - **primary**, **secondary**, **success**, **info**, **warning**, **error**) or a Boolean which uses the component **color** (set by color prop - if it's supported by the component) or the primary color.", "persistentHint": "Forces [hint](#props-hint) to always be visible.", "placeholder": "Sets the input's placeholder text.", "prependIcon": "Prepends an icon to the component, uses the same syntax as `v-icon`.", "required": "Designates the input as required; Adds an asterisk to the end of the label; Does not perform any validation.", "tabindex": "Tab index of input.", "toggleKeys": "Array of key codes that will toggle the input (if it supports toggling).", "value": "The input's value."}, "events": {"change": "Emitted when the input is changed by user interaction.", "click": "Emitted when input is clicked.", "click:append": "Emitted when appended icon is clicked.", "click:prepend": "Emitted when prepended icon is clicked.", "mousedown": "Emitted when click is pressed.", "mouseup": "Emitted when click is released."}, "exposed": {"errorMessages": "An array of error messages that were set by the `setErrors` method.", "isValid": "Boolean indicating if the input is valid.", "reset": "Resets the input value.", "resetValidation": "Resets validation of the input without modifying its value.", "validate": "Validates the input's value."}}