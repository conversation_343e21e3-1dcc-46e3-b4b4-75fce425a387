{"props": {"error": "Puts the input in a manual error state.", "errorCount": "The total number of errors that should display at once.", "errorMessages": "Puts the input in an error state and passes through custom error messages. Will be combined with any validations that occur from the **rules** prop. This field will not trigger validation.", "readonly": "Puts input in readonly state.", "rules": "Accepts a mixed array of types `function`, `boolean` and `string`. Functions pass an input value as an argument and must return either `true` / `false` or a `string` containing an error message. The input field will enter an error state if a function returns (or any value in the array contains) `false` or is a `string`.", "success": "Puts the input in a manual success state.", "successMessages": "Puts the input in a success state and passes through custom success messages.", "validateOnBlur": "Delays validation until blur event.", "maxErrors": "Control the maximum number of shown errors from validation.", "validateOn": "Change what type of event triggers validation to run.", "validationValue": "The value used when applying validation rules."}, "events": {"update:error": "The `v-model:error` event."}}