{"props": {"bottom": "Expands from the bottom of the screen on mobile devices.", "clipped": "A clipped drawer rests under the application toolbar. **Note:** requires the **clipped-left** or **clipped-right** prop on `v-app-bar` to work as intended.", "disableResizeWatcher": "Prevents the automatic opening or closing of the drawer when resized, based on whether the device is mobile or desktop.", "disableRouteWatcher": "Disables opening of navigation drawer when route changes.", "expandOnHover": "Collapses the drawer to a **rail-variant** until hovering with the mouse.", "floating": "A floating drawer has no visible container (no border-right).", "height": "Sets the height of the navigation drawer.", "image": "Apply a specific background image to the component.", "miniVariantWidth": "Designates the width assigned when the `mini` prop is turned on.", "miniVariant": "Condenses navigation drawer width, also accepts the **.sync** modifier. With this, the drawer will re-open when clicking it.", "mobileBreakpoint": "Sets the designated mobile breakpoint for the component. This will apply alternate styles for mobile devices such as the `temporary` prop, or activate the `bottom` prop when the breakpoint value is met. Setting the value to `0` will disable this functionality.", "permanent": "The drawer remains visible regardless of screen size.", "rail": "Sets the component width to the **rail-width** value.", "railWidth": "Sets the width for the component when `rail` is enabled.", "right": "Places the navigation drawer on the right.", "scrim": "Determines whether an overlay is used when a **temporary** drawer is open. Accepts true/false to enable background, and string to define color.", "temporary": "A temporary drawer sits above its application and uses a scrim (overlay) to darken the background.", "touchless": "Disable mobile touch functionality.", "value": "Controls whether the component is visible or hidden.", "location": "Controls the edge of the screen the drawer is attached to.", "sticky": "When true, the drawer will remain visible when scrolling past the top of the page."}, "slots": {"append": "A slot at the bottom of the drawer.", "image": "Used to modify `v-img` properties when using the **src** prop.", "prepend": "A slot at the top of the drawer"}, "events": {"transitionend": "Emits event object when transition is complete.", "update:rail": "Event that is emitted when the rail model changes."}, "exposed": {"isStuck": "Used to determine if the drawer is stuck to the top of the page."}}