{"props": {"continuous": "If `true`, window will \"wrap around\" from the last item to the first, and from the first item to the last.", "direction": "The transition direction when changing windows.", "nextIcon": "Icon used for the \"next\" button if `show-arrows` is `true`.", "prevIcon": "Icon used for the \"prev\" button if `show-arrows` is `true`.", "reverse": "Reverse the normal transition direction.", "reverseTransition": "The transition used when the component is reversing items. Can be one of the [built in transitions](/styles/transitions) or one your own.", "showArrows": "Display the \"next\" and \"prev\" buttons.", "showArrowsOnHover": "Display the \"next\" and \"prev\" buttons on hover. `show-arrows` MUST ALSO be set.", "touch": "Provide a custom **left** and **right** function when swiped left or right.", "touchless": "Disable touch support.", "vertical": "Uses a vertical transition when changing windows."}, "events": {"change": "Emitted when the component value is changed by user interaction."}, "slots": {"next": "Slot displaying the arrow switching to the next item.", "prev": "Slot displaying the arrow switching to the previous item.", "additional": "Slot for additional content at the end of the component."}, "exposed": {"group": "Returns item group data, state and helper methods."}}