{"props": {"customFilter": "Function to filter items.", "customGroup": "Function used to group items.", "customSort": "Function used to sort items.", "disableFiltering": "Disables filtering completely.", "disablePagination": "Disables pagination completely.", "disableSort": "Disables sorting completely.", "expanded": "Array of expanded items. Can be used with `.sync` modifier.", "footerProps": "See the [`v-data-footer`](/api/v-data-footer) API for more information.", "groupBy": "Changes which item property should be used for grouping items. Currently only supports a single grouping in the format: `group` or `['group']`. When using an array, only the first element is considered. Can be used with `.sync` modifier.", "groupDesc": "Changes which direction grouping is done. Can be used with `.sync` modifier.", "hideDefaultFooter": "Hides default footer.", "itemKey": "The property on each item that is used as a unique key.", "itemsPerPage": "Changes how many items per page should be visible. Can be used with `.sync` modifier. Setting this prop to `-1` will display all items on the page.", "loading": "If `true` and no items are provided, then a loading text will be shown.", "loadingText": "Text shown when `loading` is true and no items are provided.", "locale": "Sets the locale used for sorting. This is passed into [`Intl.Collator()`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Collator/Collator) in the default `customSort` function.", "mobileBreakpoint": "Used to set when to toggle between regular table and mobile view.", "multiSort": "If `true` then one can sort on multiple properties.", "mustSort": "If `true` then one can not disable sorting, it will always switch between ascending and descending.", "noResultsText": "Text shown when `search` prop is used and there are no results.", "search": "Text input used to filter items.", "selectableKey": "The property on each item that is used to determine if it is selectable or not.", "serverItemsLength": "Used only when data is provided by a server. Should be set to the total amount of items available on server so that pagination works correctly.", "singleExpand": "Changes expansion mode to single expand.", "singleSelect": "Changes selection mode to single select.", "sortBy": "Changes which item property (or properties) should be used for sort order. Can be used with `.sync` modifier.", "sortDesc": "Changes which direction sorting is done. Can be used with `.sync` modifier.", "value": "Used for controlling selected rows."}, "slots": {"default": "The default slot. Use this to render your items.", "footer.page-text": "This slot is forwarded to the default footer. See the [`v-data-footer`](/api/v-data-footer) API for more information.", "footer": "Defines a footer below the items.", "header": "Defines a header above the items.", "item": "Slot for each item.", "loading": "Defines content for when `loading` is true and no items are provided.", "no-data": "Defines content for when no items are provided.", "no-results": "Defines content for when `search` is provided but no results are found."}, "events": {"input": "Array of selected items.", "itemExpanded": "Event emitted when an item is expanded or closed.", "itemSelected": "Event emitted when an item is selected or deselected.", "update:currentItems": "The `.sync` event for `currentItems` prop.", "update:expanded": "The `.sync` event for `expanded` prop.", "update:groupBy": "The `.sync` event for `groupBy` prop.", "update:itemsPerPage": "The `.sync` event for `itemsPerPage` prop.", "update:options": "The `.sync` event for `options` prop.", "update:page": "The `.sync` event for `page` prop.", "update:sortBy": "The `.sync` event for `sortBy` prop."}}