{"props": {"height": "Height of the component as a css value/", "itemKey": "Should point to a property with a unique value for each item, if not set then item index will be used as a key which may result in unnecessary re-renders.", "items": "The array of items to display.", "renderless": "Disables default component rendering functionality. The parent node must be [a positioned element](https://developer.mozilla.org/en-US/docs/Web/CSS/position#types_of_positioning), e.g. using `position: relative;`"}, "slots": {"default": "Default slot to render a single item."}, "exposed": {"calculateVisibleItems": "Trigger updating the currently rendered items based on scroll position."}}