{"props": {"attach": "Specifies which DOM element the overlay content should teleport to. Can be a direct element reference, querySelector string, or `true` to disable teleporting. Uses `body` by default. Generally not recommended except as a last resort: the default positioning algorithm should handle most scenarios better than is possible without teleporting, and you may have unexpected behavior if the menu ends up as child of its activator.", "id": "The unique identifier of the component.", "closeOnClick": "Designates if menu should close on outside-activator click.", "closeOnContentClick": "Designates if menu should close when its content is clicked.", "closeDelay": "Milliseconds to wait before closing component. Only works with the **open-on-hover** prop.", "disableKeys": "Removes all keyboard interaction.", "internalActivator": "Detaches the menu content inside of the component as opposed to the document.", "minWidth": "Sets the minimum width for the component. Use `auto` to use the activator width.", "offsetX": "Offset the menu on the x-axis. Works in conjunction with direction left/right.", "offsetY": "Offset the menu on the y-axis. Works in conjunction with direction top/bottom.", "openDelay": "Milliseconds to wait before opening component. Only works with the **open-on-hover** prop.", "openOnClick": "Designates whether menu should open on activator click.", "openOnHover": "Designates whether menu should open on activator hover.", "returnValue": "The value that is updated when the menu is closed - must be primitive. Dot notation is supported.", "submenu": "Opens with right arrow and closes on left instead of up/down. Implies `location=\"end\"`. Directions are reversed for RTL."}, "exposed": {"activatorEl": "Ref to the current activator element.", "animateClick": "Function invoked when user clicks outside.", "contentEl": "Ref to the current content element.", "globalTop": "Used by activator to determine a components position in the global stack order.", "id": "The unique identifier of the component.", "localTop": "Used by activator to determine a components position in the local stack order.", "target": "Ref to the current target element.", "updateLocation": "Function used for locationStrategy positioning."}}