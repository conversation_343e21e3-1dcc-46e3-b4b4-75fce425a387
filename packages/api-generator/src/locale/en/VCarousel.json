{"props": {"color": "Applies a color to the navigation dots - supports utility colors (for example `success` or `purple`) or css color (`#033` or `rgba(255, 0, 0, 0.5)`). Find a list of built-in classes on the [colors page](/styles/colors#material-colors).", "continuous": "Determines whether carousel is continuous.", "cycle": "Determines if the carousel should cycle through images.", "delimiterIcon": "Sets icon for carousel delimiter.", "hideControls": "Hides the navigation controls (left and right).", "hideDelimiters": "Hides the carousel's bottom delimiters.", "hideDelimiterBackground": "Hides the bottom delimiter background.", "interval": "The duration between image cycles. Requires the **cycle** prop.", "nextIcon": "The displayed icon for forcing pagination to the next item.", "prevIcon": "The displayed icon for forcing pagination to the previous item.", "progress": "Displays a carousel progress bar. Requires the **cycle** prop and **interval**.", "progressColor": "Applies specified color to progress bar.", "showArrows": "Displays arrows for next/previous navigation.", "showArrowsOnHover": "Displays navigation arrows only when the carousel is hovered over.", "verticalDelimiters": "Displays carousel delimiters vertically."}}