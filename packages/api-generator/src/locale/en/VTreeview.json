{"props": {"activatable": "Allows user to mark a node as active by clicking on it.", "color": "Applies specified color to the active node - supports utility colors (for example `success` or `purple`) or css color (`#033` or `rgba(255, 0, 0, 0.5)`). Find a list of built-in classes on the [colors page](/styles/colors#material-colors).", "dense": "Decreases the height of the items.", "disabled": "Disables selection for all nodes.", "disablePerNode": "Prevents disabling children nodes.", "expandIcon": "Icon used to indicate that a node can be expanded.", "hoverable": "Applies a hover class when mousing over nodes.", "filter": "Custom item filtering function. By default it will use case-insensitive search in item's label.", "fluid": "Removes indentation from nested items.", "indeterminateIcon": "Icon used when node is in an indeterminate state. Only visible when `selectable` is `true`.", "itemChildren": "Property on supplied `items` that contains its children.", "itemDisabled": "Property on supplied `items` that contains the disabled state of the item.", "itemKey": "Property on supplied `items` used to keep track of node state. The value of this property has to be unique among all items.", "items": "An array of items used to build the treeview.", "itemText": "Property on supplied `items` that contains its label text.", "loadChildren": "A function used when dynamically loading children. If this prop is set, then the supplied function will be run if expanding an item that has a `item-children` property that is an empty array. Supports returning a Promise.", "loadingIcon": "Icon used when node is in a loading state.", "modelValue": "Allows one to control which nodes are selected. The array contains the values of currently selected items. It is equivalent to the `v-model:selected`", "multipleActive": "When `true`, allows user to have multiple active nodes at the same time.", "offIcon": "Icon used when node is not selected. Only visible when `selectable` is `true`.", "onIcon": "Icon used when leaf node is selected or when a branch node is fully selected. Only visible when `selectable` is `true`.", "open": "Syncable prop that allows one to control which nodes are open. The array consists of the `item-key` of each open item.", "openAll": "When `true` will cause all branch nodes to be opened when component is mounted.", "openOnClick": "When `true` will cause nodes to be opened by clicking anywhere on it, instead of only opening by clicking on expand icon. When using this prop with `activatable` you will be unable to mark nodes with children as active.", "returnObject": "When `true` will make `v-model`, `active.sync` and `open.sync` return the complete object instead of just the key.", "rounded": "Provides an alternative active style for `v-treeview` node. Only visible when `activatable` is `true` and should not be used in conjunction with the `shaped` prop.", "search": "The search model for filtering results.", "selectable": "Will render a checkbox next to each node allowing them to be selected. Additionally, the **[openOnClick](/api/v-treeview/#props-open-on-click)** property will be applied internally.", "selectedColor": "The color of the selection checkbox.", "selectionType": "Controls how the treeview selects nodes. There are two modes available: 'leaf' and 'independent'.", "shaped": "Provides an alternative active style for `v-treeview` node. Only visible when `activatable` is `true` and should not be used in conjunction with the `rounded` prop.", "transition": "Applies a transition when nodes are opened and closed.", "value": "Allows one to control which nodes are selected. The array consists of the `item-key` of each selected item. Is used with `@input` event to allow for `v-model` binding.", "collapseIcon": "Icon to display when the list item is expanded."}, "slots": {"append": "Appends content after label.", "prepend": "Prepends content before label.", "header": "Slot for custom header.", "subheader": "Slot for custom subheader.", "divider": "Slot for custom divider."}, "events": {"click:open": "Emits the item when it is clicked to open.", "click:select": "Emits the item when it is clicked to select.", "update:activated": "Emits the array of active items when this value changes.", "update:opened": "Emits the array of open items when this value changes.", "update:selected": "Emits the array of selected items when this value changes."}, "exposed": {"open": "Open a node by id"}}