{"props": {"backgroundColor": "Changes the background-color for the component.", "borderless": "Removes the group's border.", "dense": "Reduces the button size and padding.", "group": "Generally used in [v-toolbar](/components/toolbars) and [v-app-bar](/components/app-bars). Removes background color, border and increases space between the buttons.", "rounded": "Round edge buttons.", "shaped": "Applies a large border radius on the top left and bottom right of the card.", "tile": "Removes the component's border-radius."}, "exposed": {"next": "Activates the next button.", "prev": "Activates the previous button.", "select": "Selects a button by index, the second parameter is a boolean to indicate if the button should be selected or not."}}