{"props": {"autoDraw": "Trace the length of the line when first rendered.", "autoDrawDuration": "Amount of time (in ms) to run the trace animation.", "autoDrawEasing": "The easing function to use for the trace animation.", "autoLineWidth": "Automatically expand bars to use space efficiently.", "data": "An array of data points.", "fill": "Using the **fill** property allows you to better customize the look and feel of your sparkline.", "gradient": "An array of colors to use as a linear-gradient.", "gradientDirection": "The direction the gradient should run.", "height": "Height of the SVG trendline or bars.", "labels": "An array of string labels that correspond to the same index as its data counterpart.", "labelSize": "The label font size.", "lineWidth": "The thickness of the line, in px.", "padding": "Low `smooth` or high `line-width` values may result in cropping, increase padding to compensate.", "showLabels": "Show labels below each data point.", "smooth": "Number of px to use as a corner radius. `true` defaults to 8, `false` is 0.", "type": "Choose between a trendline or bars.", "value": "An array of numbers.", "width": "Width of the SVG trendline or bars.", "id": "The id of the component.", "itemValue": "The value of the item.", "max": "The maximum value of the sparkline.", "min": "The minimum value of the sparkline."}}