{"props": {"appendInnerIcon": "Creates a [v-icon](/api/v-icon/) component in the **append-inner** slot.", "baseColor": "Sets the color of the input when it is not focused.", "centerAffix": "Vertically align **appendInner**, **prependInner**, **clearIcon** and **label** in the center.", "clearIcon": "The icon used when the **clearable** prop is set to true.", "dirty": "Manually apply the dirty state styling.", "disabled": "Removes the ability to click or target the input.", "glow": "Makes prepend/append icons full opacity when the field is focused and apply color.", "error": "Puts the input in a manual error state.", "flat": "Removes box shadow when using a variant with elevation.", "iconColor": "Sets the color of the prepend/append icons.", "id": "Sets the DOM id on the component.", "persistentClear": "Always show the clearable icon when the input is dirty (By default it only shows on hover).", "prependInnerIcon": "Creates a [v-icon](/api/v-icon/) component in the **prepend-inner** slot.", "reverse": "Reverses the orientation.", "singleLine": "Label does not move on focus/dirty."}, "events": {"click:append": "Emitted when append icon is clicked.", "click:appendInner": "Emitted when appended inner icon is clicked.", "click:clear": "Emitted when clearable icon clicked.", "click:control": "Emitted when the main input is clicked.", "click:prepend": "Emitted when prepended icon is clicked.", "click:prependInner": "Emitted when prepended inner icon is clicked.", "group:selected": "Event that is emitted when an item is selected within a group.", "update:focused": "Emitted when the input is focused or blurred"}, "slots": {"append-inner": "Slot that is appended to the input.", "clear": "Slot for custom clear icon (displayed when the **clearable** prop is equal to true).", "label": "The default slot of the [v-label](/api/v-label/) or [v-field-label](/api/v-field-label/) component.", "prepend-inner": "Slot that is prepended to the input."}, "exposed": {"controlRef": "Reference to the control element of the field.", "fieldIconColor": "The color of the icon."}}