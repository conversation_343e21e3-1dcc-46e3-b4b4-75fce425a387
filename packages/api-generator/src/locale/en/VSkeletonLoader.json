{"props": {"boilerplate": "Remove the loading animation from the skeleton.", "loading": "Applies a loading animation with a on-hover loading cursor. A value of **false** will only work when there is content in the `default` slot.", "loadingText": "aria-label for the element in a loading state.", "type": "A string delimited list of skeleton components to create such as `type=\"text@3\"` or `type=\"card, list-item\"`. Will recursively generate a corresponding skeleton from the provided string. Also supports short-hand for multiple elements such as **article@3** and **paragraph@2** which will generate 3 _article_ skeletons and 2 _paragraph_ skeletons. Please see below for a list of available pre-defined options.", "types": "A custom types object that will be combined with the pre-defined options. For a list of available pre-defined options, see the **type** prop."}}