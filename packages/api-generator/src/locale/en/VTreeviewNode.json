{"props": {"activatable": "Allows user to mark a node as active by clicking on it.", "activeClass": "The class applied to the node when active.", "expandIcon": "Icon used to indicate that a node can be expanded.", "indeterminateIcon": "Icon used when node is in an indeterminate state. Only visible when `selectable` is `true`.", "item": "Item object used to build the node.", "itemChildren": "Property on supplied `items` that contains its children.", "itemDisabled": "Property on supplied `items` that contains the disabled state of the item.", "itemKey": "Property on supplied `items` used to keep track of node state. The value of this property has to be unique among all items.", "itemText": "Property on supplied `items` that contains its label text.", "level": "Property designating how deep the node is from the root of the treeview.", "loadChildren": "A function used when dynamically loading children. If this prop is set, then the supplied function will be run if expanding an item that has a `item-children` property that is an empty array. Supports returning a Promise.", "loadingIcon": "Icon used when node is in a loading state.", "offIcon": "Icon used when node is not selected. Only visible when `selectable` is `true`.", "onIcon": "Icon used when leaf node is selected or when a branch node is fully selected. Only visible when `selectable` is `true`.", "openOnClick": "When `true` will cause nodes to be opened by clicking anywhere on it, instead of only opening by clicking on expand icon. When using this prop with `activatable` you will be unable to mark nodes with children as active.", "parentIsDisabled": ".", "returnObject": "When `true` will make `v-model`, `active.sync` and `open.sync` return the complete object instead of just the key.", "rounded": "Provides an alternative active style for `v-treeview` node. Only visible when `activatable` is `true` and should not be used in conjunction with the `shaped` prop.", "selectable": "Will render a checkbox next to each node allowing them to be selected.", "selectedColor": "The color of the selection checkbox.", "selectionType": "Controls how the treeview selects nodes. There are two modes available: 'leaf' and 'independent'.", "shaped": "Provides an alternative active style for `v-treeview` node. Only visible when `activatable` is `true` and should not be used in conjunction with the `rounded` prop.", "transition": "Applies a transition when nodes are opened and closed."}}