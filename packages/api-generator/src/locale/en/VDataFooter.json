{"props": {"disableItemsPerPage": "Disables items-per-page dropdown.", "disablePagination": "Disables pagination buttons.", "firstIcon": "First icon.", "itemsPerPageAllText": "Text for 'All' option in items-per-page dropdown.", "itemsPerPageOptions": "Array of options to show in the items-per-page dropdown.", "itemsPerPageText": "Text for items-per-page dropdown.", "lastIcon": "Last icon.", "nextIcon": "Next icon.", "options": "DataOptions.", "pagination": "DataPagination.", "prevIcon": "Previous icon.", "showCurrentPage": "Show current page number between prev/next icons.", "showFirstLastPage": "Show first/last icons."}, "slots": {"prepend": "Adds content to the empty space in the footer.", "page-text": "Defines content for the items-per-page text."}, "events": {"update:options": "The `.sync` event for `options` prop."}}