{"props": {"disabled": "Prevents the item from becoming active when using the \"next\" and \"prev\" buttons or the `toggle` method.", "reverseTransition": "Sets the reverse transition.", "transition": "The transition used when the component progressing through items. Can be one of the [built in](/styles/transitions/) or custom transition."}, "exposed": {"groupItem": "Returns item and item group data, state and helper methods."}}