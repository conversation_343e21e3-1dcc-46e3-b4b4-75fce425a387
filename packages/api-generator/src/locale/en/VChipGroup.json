{"props": {"baseColor": "Sets the color of component when not focused. Recommended with `color` or `filter` to properly highlight selected items.", "centerActive": "Forces the selected chip to be centered.", "column": "Remove horizontal pagination and wrap items as needed.", "filter": "Applies an checkmark icon in front of every chip for using it like a filter.", "nextIcon": "Specify the icon to use for the next icon.", "prependIcon": "This icon used in the prepend slot (if shown).", "prevIcon": "Specify the icon to use for the prev icon.", "showArrows": "Force the display of the pagination arrows."}}