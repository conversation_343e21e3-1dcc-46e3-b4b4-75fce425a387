{"props": {"indeterminate": "Constantly animates, use when loading progress is unknown. If set to the string `'disable-shrink'` it will use a simpler animation that does not run on the main thread.", "modelValue": "The percentage value for current progress.", "query": "Animates like **indeterminate** prop but inverse.", "rotate": "Rotates the circle start point in degrees.", "size": "Sets the diameter of the circle in pixels.", "width": "Sets the stroke of the circle in pixels."}}