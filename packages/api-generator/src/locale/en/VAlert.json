{"props": {"border": "Adds a colored border to the component.", "borderColor": "Specifies the color of the border. Only used in combination with **border** prop. Accepts any color value.", "closable": "Adds a close icon that can hide the alert.", "closeIcon": "Change the default icon used for **closable** alerts.", "closeLabel": "Text used for *aria-label* on **closable** alerts. Can also be customized globally in [Internationalization](/customization/internationalization).", "dense": "Decreases component's height.", "elevation": "Designates an elevation applied to the component between 0 and 24. You can find more information on the [elevation page](/styles/elevation).", "height": "Sets the height for the component.", "maxHeight": "Sets the maximum height for the component.", "maxWidth": "Sets the maximum width for the component.", "minHeight": "Sets the minimum height for the component.", "minWidth": "Sets the minimum width for the component.", "modelValue": "Controls whether the component is visible or hidden.", "prominent": "Displays a larger vertically centered icon to draw more attention.", "tile": "Removes the component's border-radius.", "type": "Create a specialized alert that uses a contextual color and has a pre-defined icon.", "width": "Sets the width for the component."}, "slots": {"append": "Slot for icon at end of alert.", "close": "Slot for icon used in **dismissible** prop.", "prepend": "Slot for icon at beginning of alert."}, "functions": {"toggle": "Toggles the alert's active state. Available in the close slot and used as the click action in **dismissible**."}}