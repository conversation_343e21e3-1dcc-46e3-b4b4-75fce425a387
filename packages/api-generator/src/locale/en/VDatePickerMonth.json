{"props": {"allowedDates": "Sets the allowed dates of the month.", "firstDayOfWeek": "Sets the first day of the week, starting with 0 for Sunday.", "hideWeekdays": "Hide the days of the week letters.", "max": "Sets the maximum date of the month.", "min": "Sets the minimum date of the month.", "month": "Sets the month.", "weekdays": "Sets the weekdays of the month.", "year": "Sets the year.", "multiple": "Sets the multiple of the month.", "showAdjacentMonths": "Show adjacent months.", "showWeek": "Show the week number.", "transition": "The transition used when changing months into the future", "reverseTransition": "The transition used when changing months into the past"}, "events": {"update:month": "Fired when the month changes.", "update:year": "Fired when the year changes.", "day": "Fired when a day is clicked."}, "slots": {"year": "Slot for the year.", "day": "Slot for a day in the month."}}