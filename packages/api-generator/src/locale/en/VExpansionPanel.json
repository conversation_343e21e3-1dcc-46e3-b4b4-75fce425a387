{"props": {"disabled": "Disables the expansion-panel content.", "readonly": "Makes the expansion panel content read only.", "focusable": "Makes the expansion panel content focusable.", "value": "Controls the opened/closed state of content.", "static": "Remove title size expansion when selected."}, "events": {"change": "Toggles the value of the selected panel.", "click": "Mouse click event."}, "exposed": {"groupItem": "The current expansion panel within the group in `<v-expansion-panels>`."}}