{"props": {"allowedDates": "Restricts which dates can be selected.", "calendarIcon": "The icon shown in the header when in 'input' **input-mode**.", "dayFormat": "Allows you to customize the format of the day string that appears in the date table. Called with date (ISO 8601 **date** string) arguments.", "displayDate": "The date displayed in the picker header.", "eventColor": "Sets the color for event dot. It can be string (all events will have the same color) or `object` where attribute is the event date and value is boolean/color/array of colors for specified date or `function` taking date as a parameter and returning boolean/color/array of colors for that date.", "events": "Array of dates or object defining events or colors or function returning boolean/color/array of colors.", "expandIcon": "Icon used for **view-mode** toggle.", "headerColor": "Allows you to set a different color for the header when used in conjunction with the `color` prop.", "hideHeader": "Hides the header.", "hideWeekdays": "Hides the weekdays.", "landscape": "Changes the picker to landscape mode.", "month": "Sets the month.", "weekdays": "Array of weekdays.", "year": "Sets the year.", "firstDayOfWeek": "Sets the first day of the week, starting with 0 for Sunday.", "flat": "Removes  elevation.", "format": "Takes a date object and returns it in a specified format.", "header": "Text shown when no **display-date** is set.", "headerDateFormat": "Allows you to customize the format of the month string that appears in the header of the calendar. Called with date (ISO 8601 **date** string) arguments.", "inputMode": "Toggles between showing dates or inputs.", "locale": "Sets the locale. Accepts a string with a BCP 47 language tag.", "localeFirstDayOfYear": "Sets the day that determines the first week of the year, starting with 0 for **Sunday**. For ISO 8601 this should be 4.", "max": "Maximum allowed date/month (ISO 8601 format).", "min": "Minimum allowed date/month (ISO 8601 format).", "modeIcon": "Icon displayed next to the current month and year, toggles year selection when clicked.", "monthFormat": "Formatting function used for displaying months in the months table. Called with date (ISO 8601 **date** string) arguments.", "multiple": "Allow the selection of multiple dates. The **range** value selects all dates between two selections.", "nextIcon": "Sets the icon for next month/year button.", "pickerDate": "Displayed year/month.", "prevIcon": "Sets the icon for previous month/year button.", "reactive": "Updates the picker model when changing months/years automatically.", "readonly": "Makes the picker readonly (doesn't allow to select new date).", "scrollable": "Allows changing displayed month with mouse scroll.", "selectedItemsText": "Text used for translating the number of selected dates when using *multiple* prop. Can also be customizing globally in [Internationalization](/customization/internationalization).", "showCurrent": "Toggles visibility of the current date/month outline or shows the provided date/month as a current.", "showAdjacentMonths": "Toggles visibility of days from previous and next months.", "showWeek": "Toggles visibility of the week numbers in the body of the calendar.", "titleDateFormat": "Allows you to customize the format of the date string that appears in the title of the date picker. Called with date (ISO 8601 **date** string) arguments.", "type": "Determines the type of the picker - `date` for date picker, `month` for month picker.", "value": "Date picker model (ISO 8601 format, YYYY-mm-dd or YYYY-mm).", "viewMode": "Determines which picker in the date or month picker is being displayed. Allowed values: `'month'`, `'months'`, `'year'`.", "weekdayFormat": "Allows you to customize the format of the weekday string that appears in the body of the calendar. Called with date (ISO 8601 **date** string) arguments.", "width": "Width of the picker.", "yearFormat": "Allows you to customize the format of the year string that appears in the header of the calendar. Called with date (ISO 8601 **date** string) arguments.", "yearIcon": "Sets the icon in the year selection button."}, "events": {"update:month": "Emitted when the month changes.", "update:year": "Emitted when the year changes.", "update:viewMode": "Emitted when the view mode changes.", "<domevent>:date": "Emitted when the specified DOM event occurs on the date button.", "<domevent>:month": "Emitted when the specified DOM event occurs on the month button.", "<domevent>:year": "Emitted when the specified DOM event occurs on the year button.", "change": "Reactive date picker emits `input` even when any part of the date (year/month/day) changes, but `change` event is emitted only when the day (for date pickers) or month (for month pickers) changes. If `range` prop is set, date picker emits `change` when both [from, to] are selected.", "update:pickerDate": "The `.sync` event for `picker-date` prop."}, "slots": {"actions": "Slot for the actions.", "header": "Slot for the header.", "year": "Slot for the year."}}