{"props": {"app": "Respects boundaries of—and will not overlap with—other `app` components like `v-app-bar`, `v-navigation-drawer`, and `v-footer`.", "centered": "Positions the snackbar in the center of the screen, (x and y axis).", "multiLine": "Gives the snackbar a larger minimum height.", "timer": "Display a progress bar that counts down until the snackbar closes. Pass a string to set a custom color, otherwise uses `info`.", "timeout": "Time (in milliseconds) to wait until snackbar is automatically hidden.  Use `-1` to keep open indefinitely (`0` in version < 2.3 ). It is recommended for this number to be between `4000` and `10000`. Changes to this property will reset the timeout.", "vertical": "Stacks snackbar content on top of the actions (button)."}, "slots": {"actions": "Used to bind styles to [v-btn](/components/buttons) to match MD2 specification."}, "exposed": {"activatorEl": "Ref to the current activator element.", "animateClick": "Function invoked when user clicks outside.", "contentEl": "Ref to the current content element.", "globalTop": "Used by activator to determine a components position in the global stack order.", "localTop": "Used by activator to determine a components position in the local stack order.", "scrimEl": "Ref to the current scrim element.", "updateLocation": "Function used for locationStrategy positioning."}}