{"props": {"modelValue": "Represents the committed v-model value", "cancelText": "Text for the cancel button", "okText": "Text for the ok button", "hideActions": "Prevent showing the default actions buttons. Does not affect `<component :is=\"actions\" />`", "disabled": "Control the disabled state of action buttons. If not provided, internal logic will be used to determine the disabled state."}, "events": {"ok": "The event emitted when the user clicks the OK button", "save": "The event emitted when the user clicks the Save button", "cancel": "The event emitted when the user clicks the Cancel button"}, "exposed": {"save": "Manually commit the change", "cancel": "Manually cancel the change"}}