{"props": {"cellProps": "An object of additional props to be passed to each `<td>` in the table body. Also accepts a function that will be called for each cell. If the same prop is defined both here and in `cellProps` in a headers object, the value from the headers object will be used.", "loading": "Displays `loading` slot if set to `true`", "loadingText": "Text shown when the data is loading.", "multiSort": "Allows sorting by multiple columns.", "rowProps": "An object of additional props to be passed to each `<tr>` in the table body. Also accepts a function that will be called for each row."}, "events": {"click:row": "Emitted when a row is clicked. Native event is passed as the first argument, row data as the second."}, "slots": {"[`header.${string}`]": "Slot for custom rendering of a header cell.", "header.data-table-expand": "Slot to replace the default `v-icon` used when expanding header.", "header.data-table-select": "Slot to replace the default `v-checkbox-btn` in header.", "[`item.${string}`]": "Slot for custom rendering of a column.", "data-table-group": "Slot for custom rendering of a group.", "data-table-select": "Slot for custom rendering of a header cell with the select checkbox.", "expanded-row": "Slot for custom rendering of an expanded row.", "group-header": "Slot for custom rendering of a group header.", "item.data-table-expand": "Slot for custom rendering of a row cell with the expand icon.", "item.data-table-select": "Slot for custom rendering of a row cell with the select checkbox.", "loading": "Slot for custom rendering of the loading state."}}