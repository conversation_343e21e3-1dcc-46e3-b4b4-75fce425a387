{"props": {"dark": "Applies the dark theme variant to the component. This will default the components color to _white_ unless you've configured your [application theme](/customization/theme) to **dark** or if you are using the **color** prop on the component. You can find more information on the Material Design documentation for [dark themes](https://material.io/design/color/dark-theme.html).", "fullWidth": "Forces the dialog to expand 100% of available width.", "fullscreen": "Changes layout for fullscreen display.", "internalActivator": "Detaches the menu content inside of the component as opposed to the document.", "light": "Applies the light theme variant to the component.", "maxWidth": "Sets the maximum width for the component.", "noClickAnimation": "Disables the bounce effect when clicking outside of a `v-dialog`'s content when using the **persistent** prop.", "openOnHover": "Designates whether component should activate when its activator is hovered.", "persistent": "Clicking outside of the element or pressing **esc** key will not deactivate it.", "retainFocus": "Tab focus will return to the first child of the dialog by default. Disable this when using external tools that require focus such as TinyMCE or vue-clipboard.", "scrollable": "When set to true, expects a `v-card` and a `v-card-text` component with a designated height. For more information, check out the [scrollable example](/components/dialogs#scrollable)."}, "events": {"click:outside": "Event that fires when clicking outside an active dialog.", "keydown": "Event that fires when key is pressed. If dialog is active and not using the **persistent** prop, the **esc** key will deactivate it."}, "exposed": {"activatorEl": "Ref to the current activator element.", "animateClick": "Function invoked when user clicks outside the component and the **persistent** prop is used.", "contentEl": "Ref to the current content element.", "scrimEl": "Ref to the current scrim element.", "globalTop": "Used by activator to determine a components position in the global stack order.", "localTop": "Used by activator to determine a components position in the local stack order.", "updateLocation": "Function used for locationStrategy positioning."}}