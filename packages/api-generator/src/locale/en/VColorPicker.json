{"props": {"canvasHeight": "Height of canvas.", "dotSize": "Changes the size of the selection dot on the canvas.", "flat": "Removes elevation.", "hideCanvas": "<PERSON><PERSON> canvas.", "hideSliders": "Hides sliders.", "hideInputs": "Hides inputs.", "hideModeSwitch": "Hides mode switch.", "mode": "The current selected input type. Syncable with `v-model:mode`.", "modes": "Sets available input types.", "showSwatches": "Displays color swatches.", "swatches": "Sets the available color swatches to select from. 2D array of rows and columns, accepts any color format the picker does.", "swatchesMaxHeight": "Sets the maximum height of the swatches section.", "value": "Current color. This can be either a string representing a hex color, or an object representing a RGBA, HSLA, or HSVA value.", "width": "Sets the width of the color picker."}, "events": {"input": "Selected color. Depending on what you passed to the `value` prop this is either a string or an object.", "update:color": "Selected color. This is the internal representation of the color, containing all values.", "update:mode": "Selected mode."}}