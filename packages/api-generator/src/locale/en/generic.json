{"props": {"active": "Controls the **active** state of the item. This is typically used to highlight the component.", "activeClass": "The class applied to the component when it is in an active state.", "activeColor": "The applied color when the component is in an active state.", "appendAvatar": "Appends a [v-avatar](/components/avatars/) component after default content in the **append** slot.", "appendIcon": "Creates a [v-icon](/api/v-icon/) component after default content in the **append** slot.", "auto": "Centers list on selected element.", "baseColor": "Sets the color of component when not focused.", "bgColor": "Applies specified color to the control's background. Used on components that also support the **color** prop. - supports utility colors (for example `success` or `purple`) or css color (`#033` or `rgba(255, 0, 0, 0.5)`). Find a list of built-in classes on the [colors page](/styles/colors#material-colors).", "clearable": "Allows for the component to be cleared.", "color": "Applies specified color to the control - supports utility colors (for example `success` or `purple`) or css color (`#033` or `rgba(255, 0, 0, 0.5)`). Find a list of built-in classes on the [colors page](/styles/colors#material-colors).", "contentClass": "Applies a custom class to the detached element. This is useful because the content is moved to the beginning of the `v-app` component (unless the **attach** prop is provided) and is not targetable by classes passed directly on the component.", "counter": "Creates counter for input length; if no number is specified, it defaults to 25. Does not apply any validation.", "disabled": "Removes the ability to click or target the component.", "density": "Adjusts the vertical height used by the component.", "eager": "Forces the component's content to render when it mounts. This is useful if you have content that will not be rendered in the DOM that you want crawled for SEO.", "end": "Applies margin at the start of the component.", "falseIcon": "The icon used when inactive.", "falseValue": "Sets value for falsy state.", "fullWidth": "Sets the component width to 100%.", "height": "Sets the height for the component.", "hideNoData": "Hides the menu when there are no options to show.  Useful for preventing the menu from opening before results are fetched asynchronously.  Also has the effect of opening the menu when the `items` array changes if not already open.", "hideOnLeave": "Hides the leaving element (no exit animation).", "group": "Creates a `transition-group` component. You can find more information in the [vue docs](https://vuejs.org/api/built-in-components.html#transitiongroup).", "icon": "Apply a specific icon using the [v-icon](/components/icons/) component.", "image": "Apply a specific image using [v-img](/components/images/).", "items": "An array of strings or objects used for automatically generating children components.", "label": "Sets the text of the [v-label](/api/v-label/) or [v-field-label](/api/v-field-label/) component.", "leaveAbsolute": "Absolutely positions the leaving element (useful for [FLIP](https://aerotwist.com/blog/flip-your-animations/)).", "link": "Designates that the component is a link. This is automatic when using the href or to prop.", "mandatory": "Forces at least one item to always be selected (if available).", "menu": "Renders with the menu open by default.", "menuIcon": "Sets the the spin icon.", "messages": "Displays a list of messages or a single message if using a string.", "mode": "Sets the transition mode (does not apply to transition-group). You can find more information on the Vue documentation [for transition modes](https://vuejs.org/api/built-in-components.html#transition).", "modelModifiers": "**FOR INTERNAL USE ONLY**", "modelValue": "The v-model value of the component. If component supports the **multiple** prop, this defaults to an empty array.", "name": "Sets the component's name attribute.", "noDataText": "Text shown when no items are provided to the component.", "opacity": "Sets the component's opacity value", "origin": "Sets the transition origin on the element. You can find more information on the MDN documentation [for transition origin](https://developer.mozilla.org/en-US/docs/Web/CSS/transform-origin).", "persistent": "Clicking outside or pressing **esc** key will not dismiss the dialog.", "persistentCounter": "Forces counter to always be visible.", "prependAvatar": "Prepends a [v-avatar](/components/avatars/) component in the **prepend** slot before default content.", "prependIcon": "Creates a [v-icon](/api/v-icon/) component in the **prepend** slot before default content.", "ripple": "Applies the [v-ripple](/directives/ripple) directive.", "search": "Text input used to filter items.", "selectedClass": "Configure the active CSS class applied when an item is selected.", "size": "Sets the height and width of the component.", "subtitle": "Specify a subtitle text for the component.", "start": "Applies margin at the end of the component.", "symbol": "The [Symbol](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Symbol) used to hook into group functionality for components like [v-btn-toggle](/components/btn-toggle) and [v-bottom-navigation](/components/bottom-navigations/).", "tag": "Specify a custom tag used on the root element.", "text": "Specify content text for the component.", "textColor": "Applies a specified color to the control text - supports utility colors (for example `success` or `purple`) or css color (`#033` or `rgba(255, 0, 0, 0.5)`). Find a list of built-in classes on the [colors page](/styles/colors#material-colors).", "title": "Specify a title text for the component.", "trueIcon": "The icon used when active.", "trueValue": "Sets value for truthy state.", "valueComparator": "Apply a custom comparison algorithm to compare **model-value** and values contains in the **items** prop.", "variant": "Applies a distinct style to the component.", "width": "Sets the width for the component."}, "slots": {"activator": "When used, will activate the component when clicked (or hover for specific components). This manually stops the event propagation. Without this slot, if you open the component through its model, you will need to manually stop the event propagation.", "append": "Adds an item inside the input and after input content.", "append-item": "Adds an item after menu content.", "append-inner": "Adds an item inside the input content.", "chip": "Slot for custom chip when using the [chip](#property-chip) prop.", "details": "Slot for custom input details to modifying the display of [messages](#props-messages).", "default": "The default Vue slot.", "item": "Define a custom item appearance.", "label": "The default slot of the [v-label](/api/v-label/) or [v-field-label](/api/v-field-label/) component.", "loader": "Slot for custom loader (displayed when [loading](#props-loading) prop is equal to true).", "message": "Slot used to customize the message content.", "no-data": "Defines content for when no items are provided.", "prepend": "Adds an item outside the input and before input content.", "prepend-inner": "Adds an item inside the input content.", "prepend-item": "Adds an item before menu content.", "progress": "Slot for custom progress linear (displayed when **loading** prop is not equal to Boolean False).", "selection": "Define a custom selection appearance.", "subtitle": "Slot for the component's subtitle content.", "text": "Slot for the component's text content.", "title": "Slot for the component's title content."}, "events": {"click": "Event that is emitted when the component is clicked.", "click:close": "Emitted when close icon is clicked.", "click:append": "Emitted when appended icon is clicked.", "click:appendInner": "Emitted when appended inner icon is clicked.", "click:clear": "Emitted when clear icon is clicked.", "click:prepend": "Emitted when prepended icon is clicked.", "click:prependInner": "Emitted when prepended inner icon is clicked.", "input": "The updated bound model.", "group:selected": "Event that is emitted when an item is selected within a group.", "update:focused": "Event that is emitted when the component's focus state changes.", "update:menu": "Event that is emitted when the component's menu state changes.", "update:modelValue": "Event that is emitted when the component's model changes.", "update:search": "Event emitted when the search value changes."}, "exposed": {"filteredItems": "The current array of items based upon the current search text.", "isFocused": "Returns true if the input is focused.", "isPristine": "Returns true if the input has not been modified in any way.", "menu": "Returns true if the menu is currently open.", "search": "The current search text.", "select": "The function used to select an items. The first argument expects the value of the item."}}