{"props": {"complete": "Marks step as complete.", "completeIcon": "Icon to display when step is marked as completed.", "editable": "Marks step as editable.", "editIcon": "Icon to display when step is editable.", "errorIcon": "Icon to display when step has an error.", "error": "Puts the stepper item in a manual error state.", "rules": "Accepts a mixed array of types `function`, `boolean` and `string`. Functions pass an input value as an argument and must return either `true` / `false` or a `string` containing an error message. The input field will enter an error state if a function returns (or any value in the array contains) `false` or is a `string`.", "step": "Content to display inside step circle."}}