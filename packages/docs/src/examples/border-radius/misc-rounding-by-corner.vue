<template>
  <v-container>
    <v-row justify="space-around">
      <v-col cols="auto">
        <div class="text-center">
          <div class="bg-surface-variant rounded-ts-lg mx-auto" style="height: 64px; width: 64px;"></div>
          <div class="text-caption">rounded-ts-lg</div>
        </div>
      </v-col>

      <v-col cols="auto">
        <div class="text-center">
          <div class="bg-surface-variant rounded-te-lg mx-auto" style="height: 64px; width: 64px;"></div>
          <div class="text-caption">rounded-te-lg</div>
        </div>
      </v-col>

      <v-col cols="auto">
        <div class="text-center">
          <div class="bg-surface-variant rounded-be-lg mx-auto" style="height: 64px; width: 64px;"></div>
          <div class="text-caption">rounded-be-lg</div>
        </div>
      </v-col>

      <v-col cols="auto">
        <div class="text-center">
          <div class="bg-surface-variant rounded-bs-lg mx-auto" style="height: 64px; width: 64px;"></div>
          <div class="text-caption">rounded-bs-lg</div>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>
