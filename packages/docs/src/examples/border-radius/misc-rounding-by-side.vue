<template>
  <v-container>
    <v-row justify="space-around">
      <v-col cols="auto">
        <div class="text-center">
          <div class="bg-surface-variant rounded-t-lg mx-auto" style="height: 64px; width: 64px;"></div>
          <div class="text-caption">rounded-t-lg</div>
        </div>
      </v-col>

      <v-col cols="auto">
        <div class="text-center">
          <div class="bg-surface-variant rounded-e-lg mx-auto" style="height: 64px; width: 64px;"></div>
          <div class="text-caption">rounded-e-lg</div>
        </div>
      </v-col>

      <v-col cols="auto">
        <div class="text-center">
          <div class="bg-surface-variant rounded-b-lg mx-auto" style="height: 64px; width: 64px;"></div>
          <div class="text-caption">rounded-b-lg</div>
        </div>
      </v-col>

      <v-col cols="auto">
        <div class="text-center">
          <div class="bg-surface-variant rounded-s-lg mx-auto" style="height: 64px; width: 64px;"></div>
          <div class="text-caption">rounded-s-lg</div>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>
