<template>
  <v-container>
    <v-row justify="space-around">
      <v-col cols="auto">
        <div class="text-center">
          <div class="bg-surface-variant rounded-sm mx-auto" style="height: 64px; width: 64px;"></div>
          <div class="text-caption">rounded-sm</div>
        </div>
      </v-col>

      <v-col cols="auto">
        <div class="text-center">
          <div class="bg-surface-variant rounded mx-auto" style="height: 64px; width: 64px;"></div>
          <div class="text-caption">rounded</div>
        </div>
      </v-col>

      <v-col cols="auto">
        <div class="text-center">
          <div class="bg-surface-variant rounded-md mx-auto" style="height: 64px; width: 64px;"></div>
          <div class="text-caption">rounded-md</div>
        </div>
      </v-col>

      <v-col cols="auto">
        <div class="text-center">
          <div class="bg-surface-variant rounded-lg mx-auto" style="height: 64px; width: 64px;"></div>
          <div class="text-caption">rounded-lg</div>
        </div>
      </v-col>

      <v-col cols="auto">
        <div class="text-center">
          <div class="bg-surface-variant rounded-xl mx-auto" style="height: 64px; width: 64px;"></div>
          <div class="text-caption">rounded-xl</div>
        </div>
      </v-col>
    </v-row>
  </v-container>
</template>
